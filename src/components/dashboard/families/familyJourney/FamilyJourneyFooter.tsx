import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ChevronLeft, ChevronRight, Check } from 'lucide-react';

interface Stage {
  id: number;
  title: string;
  description: string;
  icon: React.ElementType;
  steps: Step[];
}

interface Step {
  id: string;
  title: string;
  description: string;
  type: string;
  icon: React.ElementType;
}

interface StepStatus {
  [key: string]: boolean;
}

interface JourneyConfig {
  ui: {
    navigation: {
      showPreviousButton: boolean;
      showNextButton: boolean;
      showStepCounter: boolean;
      previousButtonText: string;
      nextButtonText: string;
      doneButtonText: string;
    };
  };
}

interface FamilyJourneyFooterProps {
  currentStage: number;
  currentStep: number;
  currentStageData: Stage;
  currentStepData: Step;
  stepStatus: StepStatus;
  journeyConfig: JourneyConfig;
  onPrevStep: () => void;
  onMarkStepComplete: (stepId: string) => void;
  className?: string;
}

export function FamilyJourneyFooter({
  currentStage,
  currentStep,
  currentStageData,
  currentStepData,
  stepStatus,
  journeyConfig,
  onPrevStep,
  onMarkStepComplete,
  className,
}: FamilyJourneyFooterProps) {
  return (
    <div className={cn(
      'fixed bottom-0 left-0 right-0 z-20 border-t bg-background/95 backdrop-blur-sm h-20',
      className
    )}>
      <div className="container mx-auto p-3 lg:p-4 xl:p-6 h-full">
        <div className="flex items-center justify-between gap-2">
          {journeyConfig.ui.navigation.showPreviousButton && (
            <Button
              variant="outline"
              onClick={onPrevStep}
              disabled={currentStage === 1 && currentStep === 0}
              className="h-8 lg:h-9 px-3 lg:px-4"
              size="sm"
            >
              <ChevronLeft className="h-3 w-3 lg:h-4 lg:w-4 mr-1 lg:mr-2" />
              <span className="text-xs lg:text-sm">{journeyConfig.ui.navigation.previousButtonText}</span>
            </Button>
          )}

          {journeyConfig.ui.navigation.showStepCounter && (
            <div className="text-center min-w-0 flex-1 mx-2">
              <div className="text-xs lg:text-sm text-muted-foreground truncate">
                Step {currentStep + 1} of {currentStageData.steps.length}
              </div>
              <div className="font-medium text-xs lg:text-sm truncate">{currentStageData.title}</div>
            </div>
          )}

          {journeyConfig.ui.navigation.showNextButton && (
            <Button
              onClick={() => onMarkStepComplete(currentStepData.id)}
              disabled={stepStatus[currentStepData.id]}
              className="h-8 lg:h-9 px-3 lg:px-4"
              size="sm"
            >
              {stepStatus[currentStepData.id] ? (
                <>
                  <span className="text-xs lg:text-sm">{journeyConfig.ui.navigation.doneButtonText}</span>
                  <Check className="h-3 w-3 lg:h-4 lg:w-4 ml-1 lg:ml-2" />
                </>
              ) : (
                <>
                  <span className="text-xs lg:text-sm">{journeyConfig.ui.navigation.nextButtonText}</span>
                  <ChevronRight className="h-3 w-3 lg:h-4 lg:w-4 ml-1 lg:ml-2" />
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
